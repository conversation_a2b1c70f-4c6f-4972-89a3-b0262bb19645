/**
 * H.264 视频流解析器
 * 解析 H.264 NALU 单元，提取 SPS、PPS 和视频帧数据
 */
export class VideoParser {
  constructor() {
    this.buffer = new Uint8Array(0)
    this.sps = null
    this.pps = null
    this.onFrame = null
    this.onConfig = null
  }

  /**
   * 添加数据到缓冲区
   * @param {Uint8Array} data 
   */
  feed(data) {
    // 将新数据追加到缓冲区
    const newBuffer = new Uint8Array(this.buffer.length + data.length)
    newBuffer.set(this.buffer)
    newBuffer.set(data, this.buffer.length)
    this.buffer = newBuffer

    this.parse()
  }

  /**
   * 解析缓冲区中的 NALU 单元
   */
  parse() {
    let offset = 0
    
    while (offset < this.buffer.length - 4) {
      // 查找 NALU 起始码 (0x00000001 或 0x000001)
      const startCode = this.findStartCode(this.buffer, offset)
      if (startCode === -1) break

      const naluStart = startCode + this.getStartCodeLength(this.buffer, startCode)
      
      // 查找下一个 NALU 起始码
      const nextStartCode = this.findStartCode(this.buffer, naluStart)
      const naluEnd = nextStartCode === -1 ? this.buffer.length : nextStartCode

      // 提取 NALU 数据
      const naluData = this.buffer.slice(naluStart, naluEnd)
      if (naluData.length > 0) {
        this.processNALU(naluData)
      }

      offset = nextStartCode === -1 ? this.buffer.length : nextStartCode
    }

    // 保留未处理的数据
    if (offset > 0) {
      this.buffer = this.buffer.slice(offset)
    }
  }

  /**
   * 查找 NALU 起始码
   * @param {Uint8Array} buffer 
   * @param {number} offset 
   * @returns {number} 起始码位置，-1 表示未找到
   */
  findStartCode(buffer, offset) {
    for (let i = offset; i <= buffer.length - 3; i++) {
      // 检查 0x000001
      if (buffer[i] === 0x00 && buffer[i + 1] === 0x00 && buffer[i + 2] === 0x01) {
        return i
      }
      // 检查 0x00000001
      if (i <= buffer.length - 4 && 
          buffer[i] === 0x00 && buffer[i + 1] === 0x00 && 
          buffer[i + 2] === 0x00 && buffer[i + 3] === 0x01) {
        return i
      }
    }
    return -1
  }

  /**
   * 获取起始码长度
   * @param {Uint8Array} buffer 
   * @param {number} offset 
   * @returns {number} 3 或 4
   */
  getStartCodeLength(buffer, offset) {
    if (offset + 3 < buffer.length && 
        buffer[offset] === 0x00 && buffer[offset + 1] === 0x00 && 
        buffer[offset + 2] === 0x00 && buffer[offset + 3] === 0x01) {
      return 4
    }
    return 3
  }

  /**
   * 处理 NALU 单元
   * @param {Uint8Array} naluData 
   */
  processNALU(naluData) {
    if (naluData.length === 0) return

    const naluType = naluData[0] & 0x1F

    switch (naluType) {
      case 7: // SPS (Sequence Parameter Set)
        this.sps = naluData
        this.checkConfig()
        break

      case 8: // PPS (Picture Parameter Set)
        this.pps = naluData
        this.checkConfig()
        break

      case 5: // IDR Frame (关键帧)
      case 1: // P Frame
      case 2: // B Frame
        if (this.onFrame && this.sps && this.pps) {
          this.onFrame({
            type: naluType === 5 ? 'keyframe' : 'frame',
            data: naluData,
            timestamp: Date.now()
          })
        }
        break

      default:
        // 处理其他类型的 NALU
        if (this.onFrame && this.sps && this.pps) {
          this.onFrame({
            type: 'other',
            naluType,
            data: naluData,
            timestamp: Date.now()
          })
        }
        break
    }
  }

  /**
   * 检查是否已获取完整配置信息
   */
  checkConfig() {
    if (this.sps && this.pps && this.onConfig) {
      this.onConfig({
        sps: this.sps,
        pps: this.pps
      })
    }
  }

  /**
   * 设置帧回调
   * @param {Function} callback 
   */
  setOnFrame(callback) {
    this.onFrame = callback
  }

  /**
   * 设置配置回调
   * @param {Function} callback 
   */
  setOnConfig(callback) {
    this.onConfig = callback
  }

  /**
   * 重置解析器
   */
  reset() {
    this.buffer = new Uint8Array(0)
    this.sps = null
    this.pps = null
  }

  /**
   * 获取视频配置信息
   * @returns {Object|null}
   */
  getConfig() {
    if (this.sps && this.pps) {
      return {
        sps: this.sps,
        pps: this.pps
      }
    }
    return null
  }

  /**
   * 检查是否已准备好播放
   * @returns {boolean}
   */
  isReady() {
    return !!(this.sps && this.pps)
  }
}

export default VideoParser