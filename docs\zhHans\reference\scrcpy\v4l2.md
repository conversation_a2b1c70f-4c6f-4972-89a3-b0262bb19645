---
title: Video4Linux（视频流监控）
---

# Video4Linux

在 Linux 系统上，可以将视频流发送到 [v4l2] 回环设备，这样 Android 设备就能像普通摄像头一样被任何支持 v4l2 的工具打开。

[v4l2]: https://en.wikipedia.org/wiki/Video4Linux

需要先安装 `v4l2loopback` 模块：

```bash
sudo apt install v4l2loopback-dkms
```

创建 v4l2 设备：

```bash
sudo modprobe v4l2loopback
```

这将在 `/dev/videoN` 路径下创建一个新的视频设备，其中 `N` 为数字（可通过更多[选项](https://github.com/umlaeute/v4l2loopback#options)创建多个设备或指定设备ID）。

如果在 Chrome/WebRTC 中检测设备时遇到问题，可以尝试 `exclusive_caps` 模式：

```
sudo modprobe v4l2loopback exclusive_caps=1
```

列出已启用的设备：

```bash
# 需要安装 v4l-utils 包
v4l2-ctl --list-devices

# 简单方法（通常够用）
ls /dev/video*
```

使用 v4l2 接收器启动 `scrcpy`：

```bash
scrcpy --v4l2-sink=/dev/videoN
scrcpy --v4l2-sink=/dev/videoN --no-video-playback  # 禁用播放窗口
```

（将 `N` 替换为设备ID，可通过 `ls /dev/video*` 查看）

启用后，即可使用支持 v4l2 的工具打开视频流：

```bash
ffplay -i /dev/videoN
vlc v4l2:///dev/videoN   # VLC 可能会增加缓冲延迟
```

例如，可以在 [OBS] 或视频会议软件中捕获视频。

[OBS]: https://obsproject.com/


## 缓冲

默认情况下不启用视频缓冲，以实现最低延迟。

与[视频显示](/zhHans/reference/scrcpy/video#buffering)类似，可以为 v4l2 流添加缓冲延迟：

```bash
scrcpy --v4l2-buffer=300     # 为 v4l2 接收器添加 300ms 缓冲
```