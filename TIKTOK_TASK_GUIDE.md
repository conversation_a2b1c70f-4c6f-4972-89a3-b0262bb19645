# TikTok养号任务功能使用指南

## 🎯 功能概述

TikTok养号任务是一个智能的自动化工具，可以模拟真实用户行为在TikTok上进行养号操作，包括观看视频、点赞、关注、收藏等操作。

## ✨ 核心特性

### 📱 多设备支持
- ✅ 同时管理多个Android设备
- ✅ 自动适配不同分辨率和屏幕密度
- ✅ 独立的设备任务状态监控

### 🎮 智能操作
- ✅ 根据设备分辨率自动计算操作坐标
- ✅ 模拟真实用户的操作行为
- ✅ 随机延迟避免机器检测
- ✅ 可配置的操作频率

### 📊 任务管理
- ✅ 实时任务进度监控
- ✅ 详细的操作统计
- ✅ 任务暂停/继续/停止
- ✅ 实时日志记录

## 🛠️ 功能架构

### 1. **任务管理器 (TikTokTaskManager)**
```javascript
// 负责管理多设备任务
- 设备控制器初始化
- 任务进度监控
- 状态同步和更新
- 错误处理和恢复
```

### 2. **设备控制器 (DeviceController)**
```javascript
// 负责单设备操作
- 设备信息获取
- 坐标计算
- ADB命令执行
- 操作统计
```

### 3. **用户界面组件**
```javascript
// 任务配置和监控界面
- 参数配置对话框
- 实时状态监控
- 操作日志显示
- 任务控制按钮
```

## 🎮 使用方法

### 1. 启动任务

#### 步骤1：选择设备
1. 在设备列表中**勾选**要执行任务的设备
2. 确保设备状态为"已连接"（绿色）
3. 支持同时选择多个设备

#### 步骤2：配置任务
1. 点击**"TikTok养号"**按钮
2. 在弹出的配置对话框中设置参数：

**基础配置：**
- **任务时长**：1-480分钟（建议30-120分钟）
- **视频观看时间**：3-60秒（建议5-15秒）

**操作频率：**
- **点赞频率**：0-100%（建议10-20%）
- **关注频率**：0-50%（建议3-8%）
- **收藏频率**：0-30%（建议1-5%）

**高级配置：**
- **滑动间隔**：1-10秒（建议2-4秒）
- **操作随机延迟**：开启（推荐）
- **模拟人工行为**：开启（推荐）

#### 步骤3：开始任务
1. 点击**"开始任务"**按钮
2. 系统会自动：
   - 检查设备连接状态
   - 验证TikTok应用安装
   - 获取设备分辨率信息
   - 计算操作坐标
   - 启动自动化任务

### 2. 监控任务

#### 任务状态界面
- **总体进度**：显示任务完成百分比
- **设备状态**：每个设备的详细状态
- **实时日志**：操作记录和错误信息

#### 设备状态信息
- **当前操作**：正在执行的操作类型
- **已处理视频**：观看的视频数量
- **操作统计**：点赞、关注、收藏次数
- **进度条**：设备任务完成度

### 3. 任务控制

#### 可用操作
- **暂停任务**：暂停所有设备的操作
- **继续任务**：恢复暂停的任务
- **停止任务**：完全停止所有任务

## 📊 技术实现

### 1. 设备适配

#### 分辨率检测
```bash
# 获取屏幕分辨率
adb shell wm size

# 获取屏幕密度
adb shell wm density
```

#### 坐标计算
```javascript
// 根据分辨率计算操作坐标
coordinates = {
  like: { x: width * 0.92, y: height * 0.75 },      // 点赞按钮
  follow: { x: width * 0.92, y: height * 0.65 },    // 关注按钮
  favorite: { x: width * 0.92, y: height * 0.85 },  // 收藏按钮
  swipeStart: { x: width * 0.5, y: height * 0.8 },  // 滑动起点
  swipeEnd: { x: width * 0.5, y: height * 0.2 }     // 滑动终点
}
```

### 2. ADB操作命令

#### 基础操作
```bash
# 点击操作
adb shell input tap x y

# 滑动操作
adb shell input swipe x1 y1 x2 y2 duration

# 启动应用
adb shell am start -n com.zhiliaoapp.musically/.splash.SplashActivity

# 检查应用
adb shell pm list packages | grep tiktok
```

#### 智能操作
```javascript
// 随机延迟
const delay = randomBetween(500, 2000)
await sleep(delay)

// 模拟人工行为
const watchTime = randomBetween(
  config.videoWatchTime * 0.8, 
  config.videoWatchTime * 1.2
)
```

### 3. 任务流程

#### 主循环逻辑
```javascript
while (isRunning && !shouldStop) {
  // 1. 检查暂停状态
  if (isPaused) continue
  
  // 2. 观看视频
  await watchVideo()
  
  // 3. 执行随机操作
  await performRandomActions()
  
  // 4. 滑动到下一个视频
  await swipeToNext()
  
  // 5. 更新统计
  updateStats()
  
  // 6. 随机延迟
  await randomDelay()
}
```

## ⚠️ 注意事项

### 1. 设备要求
- ✅ Android 5.0+ 系统
- ✅ 已安装TikTok应用
- ✅ 开启USB调试
- ✅ 授权ADB连接

### 2. 网络要求
- ✅ 稳定的网络连接
- ✅ TikTok应用可正常使用
- ✅ 建议使用WiFi连接

### 3. 使用建议
- ✅ **适度使用**：避免过度频繁的操作
- ✅ **分时段执行**：不要24小时连续运行
- ✅ **参数调优**：根据实际情况调整频率
- ✅ **监控日志**：及时发现和处理异常

### 4. 风险提示
- ⚠️ **账号安全**：过度使用可能导致账号异常
- ⚠️ **平台规则**：遵守TikTok平台使用规则
- ⚠️ **设备安全**：确保设备和数据安全
- ⚠️ **法律合规**：确保使用符合当地法律法规

## 🔧 故障排除

### 常见问题

**Q: 任务启动失败**
A: 
1. 检查设备连接状态
2. 确认TikTok应用已安装
3. 验证ADB权限
4. 查看错误日志

**Q: 操作位置不准确**
A:
1. 检查设备分辨率检测
2. 确认TikTok应用版本
3. 手动调整坐标参数
4. 重新计算操作坐标

**Q: 任务中途停止**
A:
1. 检查网络连接
2. 确认设备未锁屏
3. 查看错误日志
4. 重启任务

**Q: 操作频率异常**
A:
1. 检查配置参数
2. 确认随机算法
3. 调整频率设置
4. 监控操作日志

### 性能优化

#### 1. 设备性能
- 关闭不必要的应用
- 保持设备电量充足
- 确保存储空间充足
- 定期重启设备

#### 2. 网络优化
- 使用稳定的WiFi连接
- 避免网络高峰期
- 确保TikTok可正常访问
- 监控网络延迟

#### 3. 任务优化
- 合理设置任务时长
- 适当调整操作频率
- 启用随机延迟
- 定期检查任务状态

## 📈 最佳实践

### 1. 参数配置建议

#### 新账号养号
```javascript
{
  duration: 60,           // 1小时
  videoWatchTime: 8,      // 8秒观看
  likeFrequency: 15,      // 15%点赞率
  followFrequency: 5,     // 5%关注率
  favoriteFrequency: 2,   // 2%收藏率
  swipeInterval: 3,       // 3秒滑动间隔
  randomDelay: true,      // 开启随机延迟
  humanLike: true         // 模拟人工行为
}
```

#### 老账号维护
```javascript
{
  duration: 30,           // 30分钟
  videoWatchTime: 12,     // 12秒观看
  likeFrequency: 20,      // 20%点赞率
  followFrequency: 8,     // 8%关注率
  favoriteFrequency: 5,   // 5%收藏率
  swipeInterval: 2.5,     // 2.5秒滑动间隔
  randomDelay: true,      // 开启随机延迟
  humanLike: true         // 模拟人工行为
}
```

### 2. 使用时机
- **上午9-11点**：用户活跃度高
- **下午2-4点**：内容更新频繁
- **晚上7-9点**：黄金时段
- **避开深夜**：减少异常检测

### 3. 监控要点
- **操作成功率**：应保持在95%以上
- **网络延迟**：应控制在500ms以内
- **错误频率**：每小时不超过5次
- **设备温度**：避免过热

## 🎯 总结

TikTok养号任务功能提供了：

✅ **智能化**：自动适配设备，智能计算坐标  
✅ **人性化**：模拟真实用户行为，避免检测  
✅ **可控性**：丰富的配置选项，灵活的任务控制  
✅ **可靠性**：完善的错误处理，实时状态监控  
✅ **扩展性**：支持多设备，易于功能扩展  

这是一个功能完整、技术先进的TikTok自动化养号解决方案！
