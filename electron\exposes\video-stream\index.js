import { spawn } from 'node:child_process'
import { adbPath } from '$electron/configs/index.js'
import appStore from '$electron/helpers/store.js'
import { ProcessManager } from '$electron/helpers/index.js'

const processManager = new ProcessManager()
let videoStreams = new Map() // deviceId -> stream info

// 简化的初始化函数，暂时不使用Socket.IO
function initSocketIOServer(httpServer) {
  console.log('Video stream service initialized')
  return null
}

// 启动scrcpy-server并设置端口转发
async function startScrcpyServer(deviceId, options = {}) {
  const { videoPort = 8080 } = options
  
  console.log(`Starting video stream for device: ${deviceId}`)
  
  // 停止可能存在的旧进程
  await stopVideoStream(deviceId)

  // 简化实现，暂时只记录状态
  videoStreams.set(deviceId, {
    port: videoPort,
    connected: true,
    startTime: Date.now()
  })

  return Promise.resolve({ deviceId, videoPort, status: 'started' })
}

// 停止视频流
async function stopVideoStream(deviceId) {
  console.log(`Stopping video stream for device: ${deviceId}`)
  
  const streamInfo = videoStreams.get(deviceId)
  if (streamInfo) {
    videoStreams.delete(deviceId)
    return Promise.resolve({ deviceId, status: 'stopped' })
  }
  
  return Promise.resolve({ deviceId, status: 'not_found' })
}

// 获取活跃的视频流列表
function getActiveStreams() {
  const streams = {}
  for (const [deviceId, info] of videoStreams.entries()) {
    streams[deviceId] = {
      connected: info.connected,
      port: info.port,
      startTime: info.startTime
    }
  }
  return streams
}

export default {
  initSocketIOServer,
  startScrcpyServer,
  stopVideoStream,
  getActiveStreams
}