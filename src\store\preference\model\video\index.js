export default {
  label: 'preferences.video.name',
  field: 'scrcpy',

  children: {
    noVideo: {
      label: 'preferences.video.disable-video.name',
      field: '--no-video',
      type: 'Switch',
      value: undefined,
      placeholder: 'preferences.video.disable-video.placeholder',
    },
    videoSource: {
      hidden: true,
      label: 'preferences.video.video-source.name',
      field: '--video-source',
      type: 'Select',
      value: undefined,
      placeholder: 'preferences.video.video-source.placeholder',
      options: [
        {
          label: 'preferences.video.video-source.display',
          value: 'display',
        },
        {
          label: 'preferences.video.video-source.camera',
          value: 'camera',
        },
      ],
    },
    maxSize: {
      label: 'preferences.video.resolution.name',
      field: '--max-size',
      type: 'InputNumber',
      value: undefined,
      placeholder: 'preferences.video.resolution.placeholder',
    },
    videoBitRate: {
      label: 'preferences.video.bit.name',
      field: '--video-bit-rate',
      type: 'Input',
      value: undefined,
      placeholder: 'preferences.video.bit.placeholder',
      append: 'bps',
    },
    maxFps: {
      label: 'preferences.video.refresh-rate.name',
      field: '--max-fps',
      type: 'InputNumber',
      value: undefined,
      placeholder: 'preferences.video.refresh-rate.placeholder',
      append: 'fps',
    },
    videoCode: {
      label: 'preferences.video.video-code.name',
      field: '--video-code',
      customized: true,
      type: 'VideoCodecSelect',
      value: undefined,
      placeholder: 'preferences.video.video-code.placeholder',
      options: [
        {
          label: 'h264 & OMX.qcom.video.encoder.avc',
          value: 'h264 & OMX.qcom.video.encoder.avc',
        },
        {
          label: 'h264 & c2.android.avc.encoder',
          value: 'h264 & c2.android.avc.encoder',
        },
        {
          label: 'h264 & OMX.google.h264.encoder',
          value: 'h264 & OMX.google.h264.encoder',
        },
        {
          label: 'h265 & OMX.qcom.video.encoder.hevc',
          value: 'h265 & OMX.qcom.video.encoder.hevc',
        },
        {
          label: 'h265 & c2.android.hevc.encoder',
          value: 'h265 & c2.android.hevc.encoder',
        },
      ],
    },
    videoCodec: {
      hidden: true,
      field: '--video-codec',
      value: undefined,
    },
    videoEncoder: {
      hidden: true,
      field: '--video-encoder',
      value: undefined,
    },
    displayOrientation: {
      label: 'preferences.video.display-orientation.name',
      field: '--display-orientation',
      type: 'Select',
      value: undefined,
      placeholder: 'preferences.video.display-orientation.placeholder',
      options: [
        { label: '0°', value: '0' },
        { label: '90°', value: '90' },
        { label: '180°', value: '180' },
        { label: '270°', value: '270' },
        { label: 'flip-0°', value: 'flip0' },
        { label: 'flip-90°', value: 'flip90' },
        { label: 'flip-180°', value: 'flip180' },
        { label: 'flip-270°', value: 'flip270' },
      ],
    },
    angle: {
      label: 'preferences.video.angle.name',
      field: '--angle',
      type: 'InputNumber',
      value: undefined,
      placeholder: 'preferences.video.angle.placeholder',
      tips: 'preferences.video.angle.tips',
      append: 'deg',
    },
    crop: {
      label: 'preferences.video.screen-cropping.name',
      field: '--crop',
      type: 'Input',
      value: undefined,
      placeholder: 'preferences.video.screen-cropping.placeholder',
    },
    displayId: {
      label: 'preferences.video.display.name',
      field: '--display-id',
      type: 'DisplaySelect',
      value: undefined,
      placeholder: 'preferences.video.display.placeholder',
      options: [
        { label: '0', value: '0' },
      ],
      props: {
        filterable: true,
        allowCreate: true,
      },
    },
    videoBuffer: {
      label: 'preferences.video.video-buffer.name',
      field: '--video-buffer',
      type: 'InputNumber',
      value: undefined,
      placeholder: 'preferences.video.video-buffer.placeholder',
      append: 'ms',
    },
    v4l2Buffer: {
      label: 'preferences.video.receiver-buffer.name',
      field: '--v4l2-buffer',
      type: 'InputNumber',
      value: undefined,
      placeholder: 'preferences.video.receiver-buffer.placeholder',
      append: 'ms',
    },
  },
}
