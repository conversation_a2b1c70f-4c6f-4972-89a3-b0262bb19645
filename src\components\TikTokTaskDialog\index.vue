<template>
  <div class="tiktok-task">
    <!-- 任务配置弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="TikTok养号任务配置"
      width="70%"
      :close-on-click-modal="false"
      class="el-dialog--beautify"
      append-to-body
      destroy-on-close
    >
      <div class="pr-12 pt-4">
        <el-form ref="formRef" :model="taskConfig" :rules="rules" label-width="120px">
        <!-- 基础配置 -->
        <el-form-item label="任务时长" prop="duration">
          <el-input-number
            v-model="taskConfig.duration"
            :min="1"
            :max="480"
            controls-position="right"
            style="width: 200px"
          />
          <span class="ml-2 text-gray-500">分钟</span>
        </el-form-item>

        <el-form-item label="视频观看时间" prop="videoWatchTime">
          <el-input-number
            v-model="taskConfig.videoWatchTime"
            :min="3"
            :max="60"
            controls-position="right"
            style="width: 200px"
          />
          <span class="ml-2 text-gray-500">秒</span>
        </el-form-item>

        <!-- 操作频率配置 -->
        <el-divider content-position="left">
          操作频率配置
        </el-divider>
        <el-form-item label="点赞频率" prop="likeFrequency">
          <el-slider
            v-model="taskConfig.likeFrequency"
            :min="0"
            :max="100"
            show-input
            style="width: 300px"
          />
          <span class="ml-2 text-gray-500">%</span>
        </el-form-item>

        <el-form-item label="关注频率" prop="followFrequency">
          <el-slider
            v-model="taskConfig.followFrequency"
            :min="0"
            :max="50"
            show-input
            style="width: 300px"
          />
          <span class="ml-2 text-gray-500">%</span>
        </el-form-item>

        <el-form-item label="收藏频率" prop="favoriteFrequency">
          <el-slider
            v-model="taskConfig.favoriteFrequency"
            :min="0"
            :max="30"
            show-input
            style="width: 300px"
          />
          <span class="ml-2 text-gray-500">%</span>
        </el-form-item>

        <!-- 高级配置 -->
        <el-divider content-position="left">
          高级配置
        </el-divider>
        <el-form-item label="滑动间隔" prop="swipeInterval">
          <el-input-number
            v-model="taskConfig.swipeInterval"
            :min="1"
            :max="10"
            :precision="1"
            :step="0.5"
            controls-position="right"
            style="width: 200px"
          />
          <span class="ml-2 text-gray-500">秒</span>
        </el-form-item>

        <el-form-item label="操作随机延迟" prop="randomDelay">
          <el-switch v-model="taskConfig.randomDelay" />
          <span class="ml-2 text-gray-500">增加随机延迟避免检测</span>
        </el-form-item>

        <el-form-item label="模拟人工行为" prop="humanLike">
          <el-switch v-model="taskConfig.humanLike" />
          <span class="ml-2 text-gray-500">模拟真实用户操作</span>
        </el-form-item>
      </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" :loading="starting" @click="startTask">
            开始任务
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 任务状态弹窗 -->
    <el-dialog
      v-model="taskStatusVisible"
      title="TikTok养号任务状态"
      width="90%"
      :close-on-click-modal="false"
      :show-close="false"
      class="el-dialog--beautify"
      append-to-body
      destroy-on-close
    >
      <div class="task-status">
        <!-- 总体进度 -->
        <div class="mb-6">
          <h4 class="text-lg font-semibold mb-2">
            任务进度
          </h4>
          <el-progress
            :percentage="overallProgress"
            :status="taskStatus === 'error' ? 'exception' : 'success'"
            :stroke-width="8"
          />
          <div class="mt-2 text-sm text-gray-600">
            已运行: {{ formatTime(elapsedTime) }} / {{ formatTime(taskConfig.duration * 60) }}
          </div>
        </div>

        <!-- 设备任务状态 -->
        <div class="device-tasks">
          <h4 class="text-lg font-semibold mb-4">
            设备任务状态
          </h4>
          <div class="space-y-4">
            <div
              v-for="deviceTask in deviceTasks"
              :key="deviceTask.deviceId"
              class="device-task-item p-4 border rounded-lg"
              :class="getDeviceTaskClass(deviceTask.status)"
            >
              <div class="flex justify-between items-center mb-2">
                <div class="font-medium">
                  {{ deviceTask.deviceName }}
                </div>
                <el-tag :type="getDeviceTaskTagType(deviceTask.status)">
                  {{ getDeviceTaskStatusText(deviceTask.status) }}
                </el-tag>
              </div>
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="text-gray-600">当前操作:</span>
                  <span class="ml-1">{{ deviceTask.currentAction || '等待中' }}</span>
                </div>
                <div>
                  <span class="text-gray-600">已处理视频:</span>
                  <span class="ml-1">{{ deviceTask.processedVideos }}</span>
                </div>
                <div>
                  <span class="text-gray-600">点赞次数:</span>
                  <span class="ml-1">{{ deviceTask.likeCount }}</span>
                </div>
                <div>
                  <span class="text-gray-600">关注次数:</span>
                  <span class="ml-1">{{ deviceTask.followCount }}</span>
                </div>
              </div>

              <div class="mt-2">
                <el-progress
                  :percentage="deviceTask.progress"
                  :status="deviceTask.status === 'error' ? 'exception' : 'success'"
                  size="small"
                />
              </div>

              <!-- 错误信息 -->
              <div v-if="deviceTask.error" class="mt-2 text-red-500 text-sm">
                错误: {{ deviceTask.error }}
              </div>
            </div>
          </div>
        </div>

        <!-- 实时日志 -->
        <div class="mt-6">
          <h4 class="text-lg font-semibold mb-2">
            实时日志
          </h4>
          <div class="log-container bg-gray-100 p-4 rounded max-h-40 overflow-y-auto">
            <div
              v-for="(log, index) in taskLogs"
              :key="index"
              class="log-item text-sm mb-1"
              :class="getLogClass(log.level)"
            >
              <span class="text-gray-500">[{{ formatLogTime(log.time) }}]</span>
              <span class="ml-2">{{ log.message }}</span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="taskStatus === 'running'" type="warning" @click="pauseTask">
            暂停任务
          </el-button>
          <el-button v-if="taskStatus === 'paused'" type="success" @click="resumeTask">
            继续任务
          </el-button>
          <el-button type="danger" @click="stopTask">
            停止任务
          </el-button>
          <el-button v-if="taskStatus === 'completed' || taskStatus === 'stopped'" @click="closeTaskStatus">
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { computed, onBeforeUnmount, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import TikTokTaskManager from './TikTokTaskManager.js'

const props = defineProps({
  selectedDevices: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['task-started', 'task-completed'])

// 响应式数据
const dialogVisible = ref(false)
const taskStatusVisible = ref(false)
const starting = ref(false)
const formRef = ref()
const currentDevices = ref([]) // 存储当前操作的设备列表

// 任务配置
const taskConfig = reactive({
  duration: 30, // 任务时长（分钟）
  videoWatchTime: 8, // 每个视频观看时间（秒）
  likeFrequency: 15, // 点赞频率（%）
  followFrequency: 5, // 关注频率（%）
  favoriteFrequency: 3, // 收藏频率（%）
  swipeInterval: 2.5, // 滑动间隔（秒）
  randomDelay: true, // 随机延迟
  humanLike: true, // 模拟人工行为
})

// 表单验证规则
const rules = {
  duration: [
    { required: true, message: '请输入任务时长', trigger: 'blur' },
    { type: 'number', min: 1, max: 480, message: '任务时长应在1-480分钟之间', trigger: 'blur' },
  ],
  videoWatchTime: [
    { required: true, message: '请输入视频观看时间', trigger: 'blur' },
    { type: 'number', min: 3, max: 60, message: '视频观看时间应在3-60秒之间', trigger: 'blur' },
  ],
}

// 任务状态
const taskStatus = ref('idle') // idle, running, paused, completed, stopped, error
const elapsedTime = ref(0)
const deviceTasks = ref([])
const taskLogs = ref([])
const taskManager = ref(null)

// 计算属性
const overallProgress = computed(() => {
  if (taskConfig.duration === 0)
    return 0
  return Math.min(100, (elapsedTime.value / (taskConfig.duration * 60)) * 100)
})

// 获取有效的设备列表
const effectiveDevices = computed(() => {
  return currentDevices.value.length > 0 ? currentDevices.value : props.selectedDevices
})

// 方法
const showDialog = () => {
  const devices = props.selectedDevices
  if (devices.length === 0) {
    ElMessage.warning('请先选择要执行任务的设备')
    return
  }
  // 更新当前设备列表
  currentDevices.value = devices
  dialogVisible.value = true
}

// 添加open方法以保持与其他组件的一致性
const open = (options = {}) => {
  console.log('TikTokTaskDialog open 被调用了！', options)

  // 如果传入了设备列表，使用传入的设备
  const devices = options.devices || props.selectedDevices

  if (devices.length === 0) {
    ElMessage.warning('请先选择要执行任务的设备')
    return
  }

  // 更新当前设备列表
  currentDevices.value = devices

  console.log('准备显示TikTok任务配置对话框，设备数量:', devices.length)
  dialogVisible.value = true
}

const startTask = async () => {
  try {
    await formRef.value.validate()
    starting.value = true

    // 创建任务管理器
    taskManager.value = new TikTokTaskManager({
      devices: effectiveDevices.value,
      config: { ...taskConfig },
      onStatusUpdate: handleTaskStatusUpdate,
      onLog: handleTaskLog,
    })

    // 初始化设备任务状态
    deviceTasks.value = effectiveDevices.value.map(device => ({
      deviceId: device.id,
      deviceName: device.name || device.id,
      status: 'preparing',
      progress: 0,
      currentAction: '准备中...',
      processedVideos: 0,
      likeCount: 0,
      followCount: 0,
      favoriteCount: 0,
      error: null,
    }))

    // 先切换到任务状态对话框
    dialogVisible.value = false
    taskStatusVisible.value = true
    taskStatus.value = 'running'

    // 开始任务
    try {
      await taskManager.value.start()

      emit('task-started', {
        devices: effectiveDevices.value,
        config: taskConfig,
      })

      ElMessage.success('TikTok养号任务已开始')
    }
    catch (taskError) {
      // 任务启动失败，更新状态
      taskStatus.value = 'error'
      ElMessage.error(`启动任务失败: ${taskError.message}`)

      // 更新设备任务状态为错误
      deviceTasks.value.forEach(task => {
        task.status = 'error'
        task.currentAction = '启动失败'
        task.error = taskError.message
      })
    }
  }
  catch (error) {
    // 表单验证或其他错误
    ElMessage.error(`配置错误: ${error.message}`)
  }
  finally {
    starting.value = false
  }
}

const pauseTask = async () => {
  if (taskManager.value) {
    await taskManager.value.pause()
    taskStatus.value = 'paused'
    ElMessage.info('任务已暂停')
  }
}

const resumeTask = async () => {
  if (taskManager.value) {
    await taskManager.value.resume()
    taskStatus.value = 'running'
    ElMessage.success('任务已继续')
  }
}

const stopTask = async () => {
  try {
    await ElMessageBox.confirm('确定要停止所有设备的TikTok养号任务吗？', '确认停止', {
      type: 'warning',
    })

    if (taskManager.value) {
      await taskManager.value.stop()
      taskStatus.value = 'stopped'
      ElMessage.success('任务已停止')
    }
  }
  catch {
    // 用户取消
  }
}

const closeTaskStatus = () => {
  taskStatusVisible.value = false
  taskStatus.value = 'idle'
  elapsedTime.value = 0
  deviceTasks.value = []
  taskLogs.value = []

  emit('task-completed')
}

// 任务状态更新处理
const handleTaskStatusUpdate = (update) => {
  const { type, deviceId, data } = update

  switch (type) {
    case 'device_status':
      // eslint-disable-next-line no-use-before-define
      updateDeviceTaskStatus(deviceId, data)
      break
    case 'overall_progress':
      elapsedTime.value = data.elapsedTime
      break
    case 'task_completed':
      taskStatus.value = 'completed'
      ElMessage.success('所有设备任务已完成')
      break
    case 'task_error':
      taskStatus.value = 'error'
      ElMessage.error(`任务执行出错: ${data.error}`)
      break
  }
}

// 更新设备任务状态
const updateDeviceTaskStatus = (deviceId, data) => {
  const deviceTask = deviceTasks.value.find(task => task.deviceId === deviceId)
  if (deviceTask) {
    Object.assign(deviceTask, data)
  }
}

// 任务日志处理
const handleTaskLog = (log) => {
  taskLogs.value.push({
    time: new Date(),
    level: log.level || 'info',
    message: log.message,
  })

  // 限制日志数量
  if (taskLogs.value.length > 100) {
    taskLogs.value.shift()
  }
}

// 辅助方法
const formatTime = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

const formatLogTime = (time) => {
  return time.toLocaleTimeString()
}

const getDeviceTaskClass = (status) => {
  const classes = {
    preparing: 'border-blue-200 bg-blue-50',
    running: 'border-green-200 bg-green-50',
    paused: 'border-yellow-200 bg-yellow-50',
    completed: 'border-green-200 bg-green-50',
    error: 'border-red-200 bg-red-50',
  }
  return classes[status] || 'border-gray-200 bg-gray-50'
}

const getDeviceTaskTagType = (status) => {
  const types = {
    preparing: 'info',
    running: 'success',
    paused: 'warning',
    completed: 'success',
    error: 'danger',
  }
  return types[status] || 'info'
}

const getDeviceTaskStatusText = (status) => {
  const texts = {
    preparing: '准备中',
    running: '运行中',
    paused: '已暂停',
    completed: '已完成',
    error: '错误',
  }
  return texts[status] || status
}

const getLogClass = (level) => {
  const classes = {
    info: 'text-blue-600',
    success: 'text-green-600',
    warning: 'text-yellow-600',
    error: 'text-red-600',
  }
  return classes[level] || 'text-gray-600'
}

// 组件卸载时清理
onBeforeUnmount(() => {
  if (taskManager.value) {
    taskManager.value.stop()
  }
})

// 暴露方法给父组件
defineExpose({
  showDialog,
  open,
})
</script>

<style scoped>
.tiktok-task {
  /* 组件样式 */
}

.log-container {
  font-family: 'Courier New', monospace;
}

.log-item {
  white-space: nowrap;
}

.device-task-item {
  transition: all 0.3s ease;
}

.device-task-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
