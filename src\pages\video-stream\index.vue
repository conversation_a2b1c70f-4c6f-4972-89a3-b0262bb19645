<template>
  <div class="video-stream-page">
    <div class="page-header">
      <h2>设备视频流</h2>
      <div class="header-actions">
        <el-button @click="refreshDevices" :loading="devicesLoading">
          <el-icon><Refresh /></el-icon>
          刷新设备
        </el-button>
        <el-button @click="showAddDeviceDialog" type="primary">
          <el-icon><Plus /></el-icon>
          添加设备
        </el-button>
        <el-button @click="disconnectAll" :disabled="activeDevices.length === 0">
          <el-icon><Close /></el-icon>
          断开所有
        </el-button>
      </div>
    </div>

    <div class="video-content">
      <div v-if="error" class="api-error">
        <el-alert
          :title="error"
          type="error"
          :closable="false"
          show-icon
        />
      </div>
      
      <div v-else-if="activeDevices.length === 0" class="no-device-connected">
        <el-empty description="暂无设备连接，请点击添加设备开始播放视频流" />
      </div>
      
      <!-- 多设备网格布局 -->
      <div v-else class="video-grid" :class="`grid-${getGridColumns(activeDevices.length)}`">
        <div
          v-for="deviceId in activeDevices"
          :key="deviceId"
          class="video-item"
        >
          <div class="video-header">
            <h3>{{ getDeviceDisplayName(deviceId) }}</h3>
            <div class="video-actions">
              <el-tag 
                :type="getDeviceConnectionStatus(deviceId) === 'connected' ? 'success' : 
                      getDeviceConnectionStatus(deviceId) === 'connecting' ? 'warning' : 'danger'"
                size="small"
              >
                {{ getStatusText(getDeviceConnectionStatus(deviceId)) }}
              </el-tag>
              <el-button
                size="small"
                type="danger"
                @click="removeDevice(deviceId)"
                :icon="Close"
                circle
              />
            </div>
          </div>
          
          <VideoPlayer
            :ref="el => setVideoPlayerRef(deviceId, el)"
            :device-id="deviceId"
            :auto-start="true"
            @connected="onVideoConnected"
            @disconnected="onVideoDisconnected"
            @error="onVideoError"
          />
        </div>
      </div>
    </div>

    <!-- 添加设备对话框 -->
    <el-dialog
      v-model="addDeviceDialogVisible"
      title="添加监控设备"
      width="500px"
    >
      <div class="device-selection">
        <div class="section-title">选择要添加的设备：</div>
        <div class="device-list">
          <div
            v-for="device in availableDevicesForAdd"
            :key="device.id"
            class="device-item"
            :class="{ active: selectedDevicesForAdd.includes(device.id) }"
            @click="toggleDeviceSelection(device.id)"
          >
            <div class="device-info">
              <div class="device-name">{{ device.model || '未知设备' }}</div>
              <div class="device-id">{{ device.id }}</div>
              <div class="device-status">
                <el-tag :type="device.state === 'device' ? 'success' : 'warning'" size="small">
                  {{ device.state === 'device' ? '在线' : device.state }}
                </el-tag>
              </div>
            </div>
            <el-checkbox :model-value="selectedDevicesForAdd.includes(device.id)" />
          </div>
        </div>
        
        <div v-if="availableDevicesForAdd.length === 0" class="no-devices">
          <el-empty description="没有可添加的设备" />
        </div>
      </div>
      
      <template #footer>
        <el-button @click="addDeviceDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="addSelectedDevices"
          :disabled="selectedDevicesForAdd.length === 0"
        >
          添加 {{ selectedDevicesForAdd.length }} 个设备
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElNotification } from 'element-plus'
import { Refresh, Plus, Close } from '@element-plus/icons-vue'
import VideoPlayer from '$/components/VideoPlayer/index.vue'
import { useDeviceStore } from '$/store/index.js'

// Store and Router
const deviceStore = useDeviceStore()
const route = useRoute()
const router = useRouter()

// API访问 - 直接从window对象访问暴露的API
const adbAPI = window.adb
const videoStreamAPI = window.videoStream

// 响应式数据
const availableDevices = ref([])
const devicesLoading = ref(false)
const activeDevices = ref([]) // 当前播放视频流的设备列表
const deviceConnections = ref(new Map()) // deviceId -> connection status
const addDeviceDialogVisible = ref(false)
const selectedDevicesForAdd = ref([])
const error = ref('')

// 引用
const videoPlayerRefs = ref(new Map()) // deviceId -> VideoPlayer ref

// 计算属性
const availableDevicesForAdd = computed(() => {
  return availableDevices.value.filter(device => 
    !activeDevices.value.includes(device.id) &&
    device.state === 'device' // 只显示在线设备
  )
})

onMounted(async () => {
  // 调试信息：检查哪些API已暴露
  console.log('Available window APIs:', Object.keys(window).filter(key => 
    !key.startsWith('_') && typeof window[key] === 'object' && window[key] !== null
  ))
  
  // 检查API是否可用
  if (!adbAPI) {
    console.error('ADB API not found. Available APIs:', Object.keys(window))
    error.value = '无法访问设备管理API，请确保应用正常启动'
    ElMessage.error('设备管理API不可用')
    return
  }
  
  console.log('ADB API found:', adbAPI)

  await loadDevices()
  
  // 处理从设备列表页面传来的参数
  const { deviceId, autoStart } = route.query
  if (deviceId && autoStart === 'true') {
    // 自动添加并连接指定设备
    activeDevices.value = [deviceId]
    deviceConnections.value.set(deviceId, 'connecting')
  } else {
    // 自动连接所有已连接的设备
    await autoConnectAvailableDevices()
  }
  
  // 定期刷新设备列表
  const refreshInterval = setInterval(loadDevices, 10000)
  
  onUnmounted(() => {
    clearInterval(refreshInterval)
    disconnectAll()
  })
})

// 设置VideoPlayer引用
function setVideoPlayerRef(deviceId, el) {
  if (el) {
    videoPlayerRefs.value.set(deviceId, el)
  } else {
    videoPlayerRefs.value.delete(deviceId)
  }
}

async function loadDevices() {
  try {
    devicesLoading.value = true
    
    // 检查API是否可用
    if (!adbAPI || !adbAPI.getDevices) {
      throw new Error('ADB API not available')
    }
    
    const devices = await adbAPI.getDevices()
    
    availableDevices.value = devices.map(device => ({
      id: device.id,
      model: device.properties?.['ro.product.model'] || '未知设备',
      type: device.type,
      state: device.type
    }))

    // 清理已断开设备的连接状态
    for (const deviceId of activeDevices.value) {
      const device = availableDevices.value.find(d => d.id === deviceId)
      if (!device || device.state !== 'device') {
        removeDevice(deviceId, false) // 静默移除
      }
    }

  } catch (error) {
    console.error('加载设备列表失败:', error)
    ElMessage.error('加载设备列表失败: ' + error.message)
  } finally {
    devicesLoading.value = false
  }
}

async function refreshDevices() {
  await loadDevices()
  ElMessage.success('设备列表已刷新')
}

// 自动连接所有可用设备
async function autoConnectAvailableDevices() {
  const connectedDevices = availableDevices.value
    .filter(device => device.state === 'device')
    .slice(0, 4) // 最多同时连接4个设备

  if (connectedDevices.length > 0) {
    activeDevices.value = connectedDevices.map(d => d.id)
    connectedDevices.forEach(device => {
      deviceConnections.value.set(device.id, 'connecting')
    })
    
    ElNotification({
      title: '自动连接',
      message: `正在自动连接 ${connectedDevices.length} 个设备`,
      type: 'info'
    })
  }
}

// 网格列数计算
function getGridColumns(deviceCount) {
  if (deviceCount === 1) return 1
  if (deviceCount === 2) return 2
  if (deviceCount <= 4) return 2
  if (deviceCount <= 6) return 3
  return 4
}

// 设备连接状态
function getDeviceConnectionStatus(deviceId) {
  return deviceConnections.value.get(deviceId) || 'disconnected'
}

function onVideoConnected(deviceId) {
  deviceConnections.value.set(deviceId, 'connected')
  ElNotification({
    title: '连接成功',
    message: `已连接到设备 ${getDeviceDisplayName(deviceId)}`,
    type: 'success'
  })
}

function onVideoDisconnected(deviceId) {
  deviceConnections.value.set(deviceId, 'disconnected')
  ElNotification({
    title: '连接断开',
    message: `已断开设备 ${getDeviceDisplayName(deviceId)} 的连接`,
    type: 'info'
  })
}

function onVideoError({ deviceId, message }) {
  deviceConnections.value.set(deviceId, 'error')
  ElNotification({
    title: '连接错误',
    message: `设备 ${getDeviceDisplayName(deviceId)}: ${message}`,
    type: 'error'
  })
}

function getDeviceDisplayName(deviceId) {
  const device = availableDevices.value.find(d => d.id === deviceId)
  return device ? `${device.model}` : deviceId
}

function getStatusText(status) {
  const statusMap = {
    connected: '已连接',
    connecting: '连接中',
    disconnected: '未连接',
    error: '连接错误'
  }
  return statusMap[status] || '未知状态'
}

// 设备管理
function showAddDeviceDialog() {
  if (availableDevicesForAdd.value.length === 0) {
    ElMessage.warning('没有可添加的设备')
    return
  }
  selectedDevicesForAdd.value = []
  addDeviceDialogVisible.value = true
}

function toggleDeviceSelection(deviceId) {
  const index = selectedDevicesForAdd.value.indexOf(deviceId)
  if (index > -1) {
    selectedDevicesForAdd.value.splice(index, 1)
  } else {
    selectedDevicesForAdd.value.push(deviceId)
  }
}

function addSelectedDevices() {
  const devicesToAdd = selectedDevicesForAdd.value.filter(id => 
    !activeDevices.value.includes(id)
  )
  
  if (devicesToAdd.length === 0) {
    ElMessage.warning('所选设备已在播放列表中')
    return
  }

  // 限制最大同时连接数
  const maxDevices = 6
  if (activeDevices.value.length + devicesToAdd.length > maxDevices) {
    ElMessage.warning(`最多只能同时连接 ${maxDevices} 个设备`)
    return
  }

  activeDevices.value.push(...devicesToAdd)
  devicesToAdd.forEach(deviceId => {
    deviceConnections.value.set(deviceId, 'connecting')
  })

  addDeviceDialogVisible.value = false
  selectedDevicesForAdd.value = []
  
  ElMessage.success(`已添加 ${devicesToAdd.length} 个设备到播放列表`)
}

function removeDevice(deviceId, showMessage = true) {
  const index = activeDevices.value.indexOf(deviceId)
  if (index > -1) {
    activeDevices.value.splice(index, 1)
    deviceConnections.value.delete(deviceId)
    
    // 断开视频流
    const playerRef = videoPlayerRefs.value.get(deviceId)
    if (playerRef && playerRef.isConnected()) {
      playerRef.stopVideoStream()
    }
    
    if (showMessage) {
      ElMessage.success(`设备 ${getDeviceDisplayName(deviceId)} 已移除`)
    }
  }
}

function disconnectAll() {
  activeDevices.value.forEach(deviceId => {
    const playerRef = videoPlayerRefs.value.get(deviceId)
    if (playerRef && playerRef.isConnected()) {
      playerRef.stopVideoStream()
    }
  })
  
  activeDevices.value = []
  deviceConnections.value.clear()
  
  ElMessage.success('已断开所有设备连接')
}

// 页面定义
definePage({
  meta: {
    title: '视频流',
    icon: 'video'
  }
})
</script>

<style scoped>
.video-stream-page {
  padding: 24px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.video-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.no-device-connected {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 网格布局 */
.video-grid {
  flex: 1;
  display: grid;
  gap: 16px;
  height: 100%;
  overflow: auto;
}

.video-grid.grid-1 {
  grid-template-columns: 1fr;
}

.video-grid.grid-2 {
  grid-template-columns: 1fr 1fr;
}

.video-grid.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.video-grid.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .video-grid.grid-3,
  .video-grid.grid-4 {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .video-grid {
    grid-template-columns: 1fr !important;
  }
}

.video-item {
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 300px;
}

.video-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.video-header h3 {
  margin: 0;
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.video-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 设备选择对话框样式 */
.device-selection {
  max-height: 400px;
  overflow: hidden;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 16px;
}

.device-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
}

.device-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s;
}

.device-item:last-child {
  border-bottom: none;
}

.device-item:hover {
  background: #f8f9fa;
}

.device-item.active {
  background: #e7f3ff;
  border-color: #409eff;
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.device-id {
  font-size: 12px;
  color: #909399;
  font-family: monospace;
  margin-bottom: 4px;
}

.device-status {
  display: flex;
  align-items: center;
}

.no-devices {
  padding: 40px 0;
  text-align: center;
}
</style>