<template>
  <div @click="handleClick(devices)">
    <slot :loading="loading" />
    <!-- TikTok任务组件 -->
    <TikTokTaskDialog
      ref="tikTokTaskRef"
      :selected-devices="devices"
      @task-started="handleTaskStarted"
      @task-completed="handleTaskCompleted"
    />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import TikTokTaskDialog from '$/components/TikTokTaskDialog/index.vue'

const props = defineProps({
  devices: {
    type: Array,
    default: () => [],
  },
})

// 监听设备变化
watch(() => props.devices, (newDevices) => {
  console.log('TikTokTaskDialog 接收到设备:', newDevices)
}, { immediate: true })

const loading = ref(false)
const tikTokTaskRef = ref()

// 处理点击事件
function handleClick(devices) {
  console.log('TikTokTaskDialog handleClick 被调用了！')
  console.log('选中的设备:', devices)

  if (devices.length === 0) {
    ElMessage.warning('请先选择要执行TikTok养号任务的设备')
    return
  }

  // 检查设备状态
  const connectedDevices = devices.filter(device =>
    ['device', 'emulator'].includes(device.status),
  )

  if (connectedDevices.length === 0) {
    ElMessage.warning('所选设备中没有已连接的设备')
    return
  }

  if (connectedDevices.length < devices.length) {
    ElMessage.warning(`${devices.length - connectedDevices.length} 个设备未连接，将只对已连接设备执行任务`)
  }

  console.log('准备显示TikTok任务配置对话框')
  // 使用open方法，与其他组件保持一致
  if (tikTokTaskRef.value) {
    tikTokTaskRef.value.open({ devices })
  }
  else {
    console.error('tikTokTaskRef.value 为空')
    ElMessage.error('TikTok任务组件未正确加载')
  }
}

// 任务开始处理
const handleTaskStarted = (taskInfo) => {
  loading.value = true
  ElMessage.success(`TikTok养号任务已开始，涉及 ${taskInfo.devices.length} 个设备`)
}

// 任务完成处理
const handleTaskCompleted = () => {
  loading.value = false
  ElMessage.info('TikTok养号任务已结束')
}

// 暴露点击方法给父组件
defineExpose({
  handleClick,
})
</script>
