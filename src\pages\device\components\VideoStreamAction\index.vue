<template>
  <el-tooltip :content="$t('device.video-stream.tooltip')" placement="top">
    <el-button
      type="primary"
      :icon="VideoPlay"
      circle
      size="small"
      @click="openVideoStream"
      :loading="loading"
    />
  </el-tooltip>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { VideoPlay } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  row: {
    type: Object,
    required: true
  }
})

const router = useRouter()
const loading = ref(false)

async function openVideoStream() {
  try {
    loading.value = true
    
    // 导航到视频流页面，并传递设备ID
    await router.push({
      path: '/video-stream',
      query: {
        deviceId: props.row.id,
        autoStart: 'true'
      }
    })
    
    ElMessage.success(`正在打开设备 ${props.row.name || props.row.id} 的视频流`)
  } catch (error) {
    console.error('打开视频流失败:', error)
    ElMessage.error('打开视频流失败: ' + error.message)
  } finally {
    loading.value = false
  }
}
</script>

<script>
export default {
  name: 'VideoStreamAction'
}
</script>