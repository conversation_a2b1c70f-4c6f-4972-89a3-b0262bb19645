<template>
  <div class="video-player-container">
    <div class="video-wrapper">
      <video 
        ref="videoRef"
        class="video-element"
        :class="{ 'video-loading': isLoading }"
        autoplay
        muted
        playsinline
      />
      <div v-if="isLoading" class="loading-overlay">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <span>连接设备中...</span>
      </div>
      <div v-if="error" class="error-overlay">
        <el-icon class="error-icon"><Warning /></el-icon>
        <span>{{ error }}</span>
      </div>
    </div>
    <div class="video-controls">
      <el-button 
        :type="isConnected ? 'danger' : 'primary'" 
        @click="toggleConnection"
        :loading="isConnecting"
      >
        {{ isConnected ? '断开连接' : '开始播放' }}
      </el-button>
      <div class="video-info">
        <span v-if="deviceInfo">{{ deviceInfo.model }} ({{ deviceInfo.id }})</span>
        <span v-if="streamStats" class="stream-stats">
          FPS: {{ streamStats.fps }} | 延迟: {{ streamStats.latency }}ms
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
// import JMuxer from 'jmuxer'
// import { io } from 'socket.io-client'
import { VideoParser } from '$/utils/video-parser/index.js'
import { ElMessage } from 'element-plus'
import { Loading, Warning } from '@element-plus/icons-vue'

// API访问 - 直接从window对象访问暴露的API
const videoStreamAPI = window.videoStream

const props = defineProps({
  deviceId: {
    type: String,
    required: true
  },
  autoStart: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['connected', 'disconnected', 'error'])

// 引用
const videoRef = ref(null)

// 状态
const isLoading = ref(false)
const isConnected = ref(false)
const isConnecting = ref(false)
const error = ref('')
const deviceInfo = ref(null)
const streamStats = ref(null)

// 实例
let videoParser = null
let statsInterval = null
let connectionTimer = null

// 性能监控
const frameCount = ref(0)
const lastFrameTime = ref(0)
const latencySum = ref(0)
const latencyCount = ref(0)

onMounted(() => {
  initializeVideoPlayer()
  if (props.autoStart) {
    startVideoStream()
  }
})

onUnmounted(() => {
  cleanup()
})

watch(() => props.deviceId, (newDeviceId) => {
  if (isConnected.value) {
    stopVideoStream()
    setTimeout(() => {
      if (newDeviceId) {
        startVideoStream()
      }
    }, 500)
  }
})

function initializeVideoPlayer() {
  console.log('Initializing video player for device:', props.deviceId)
  // 暂时简化实现
}


async function startVideoStream() {
  if (isConnecting.value || isConnected.value) return

  try {
    isConnecting.value = true
    isLoading.value = true
    error.value = ''

    console.log('Starting video stream for device:', props.deviceId)

    // 检查API是否可用
    if (!videoStreamAPI || !videoStreamAPI.startScrcpyServer) {
      throw new Error('Video Stream API not available')
    }

    // 启动后端视频流服务
    const result = await videoStreamAPI.startScrcpyServer(props.deviceId, {
      videoPort: 8080 + Math.floor(Math.random() * 1000)
    })

    console.log('Video stream service result:', result)

    // 模拟连接成功
    connectionTimer = setTimeout(() => {
      isLoading.value = false
      isConnected.value = true
      emit('connected', props.deviceId)
      startStatsMonitoring()
    }, 2000)

  } catch (err) {
    console.error('启动视频流失败:', err)
    handleError('启动视频流失败: ' + err.message)
  } finally {
    isConnecting.value = false
  }
}

function stopVideoStream() {
  console.log('Stopping video stream for device:', props.deviceId)

  if (connectionTimer) {
    clearTimeout(connectionTimer)
    connectionTimer = null
  }

  if (statsInterval) {
    clearInterval(statsInterval)
    statsInterval = null
  }

  // 停止后端视频流
  if (videoStreamAPI && videoStreamAPI.stopVideoStream) {
    videoStreamAPI.stopVideoStream(props.deviceId)
  }

  isConnected.value = false
  isLoading.value = false
  streamStats.value = null

  emit('disconnected', props.deviceId)
}

function toggleConnection() {
  if (isConnected.value) {
    stopVideoStream()
  } else {
    startVideoStream()
  }
}

function handleError(message) {
  error.value = message
  isLoading.value = false
  isConnecting.value = false
  ElMessage.error(message)
  emit('error', { deviceId: props.deviceId, message })
}

function updateStats(timestamp) {
  frameCount.value++
  
  if (timestamp) {
    const now = Date.now()
    const latency = now - timestamp
    latencySum.value += latency
    latencyCount.value++
  }
}

function startStatsMonitoring() {
  let lastFrameCount = 0
  let lastTime = Date.now()

  statsInterval = setInterval(() => {
    const now = Date.now()
    const timeDiff = (now - lastTime) / 1000
    const frameDiff = frameCount.value - lastFrameCount
    
    const fps = Math.round(frameDiff / timeDiff)
    const avgLatency = latencyCount.value > 0 
      ? Math.round(latencySum.value / latencyCount.value) 
      : 0

    streamStats.value = {
      fps,
      latency: avgLatency
    }

    lastFrameCount = frameCount.value
    lastTime = now
    
    // 重置延迟统计
    latencySum.value = 0
    latencyCount.value = 0
  }, 1000)
}

function cleanup() {
  stopVideoStream()
}

// 暴露方法给父组件
defineExpose({
  startVideoStream,
  stopVideoStream,
  isConnected: () => isConnected.value,
  isLoading: () => isLoading.value
})
</script>

<style scoped>
.video-player-container {
  display: flex;
  flex-direction: column;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.video-wrapper {
  position: relative;
  width: 100%;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.video-loading {
  visibility: hidden;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  gap: 12px;
}

.loading-icon,
.error-icon {
  font-size: 32px;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-icon {
  color: #f56565;
}

.video-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: #f5f5f5;
  border-top: 1px solid #e0e0e0;
}

.video-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: 12px;
  color: #666;
  gap: 4px;
}

.stream-stats {
  color: #409eff;
  font-family: monospace;
}
</style>