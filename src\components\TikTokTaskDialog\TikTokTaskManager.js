/**
 * TikTok养号任务管理器
 * 负责管理多设备的TikTok自动化任务
 */

import DeviceController from './DeviceController.js'

export default class TikTokTaskManager {
  constructor(options) {
    this.devices = options.devices || []
    this.config = options.config || {}
    this.onStatusUpdate = options.onStatusUpdate || (() => {})
    this.onLog = options.onLog || (() => {})
    this.isRunning = false
    this.isPaused = false
    this.startTime = null
    this.deviceControllers = new Map()
    this.taskTimer = null
    this.log('info', 'TikTok任务管理器已初始化')
  }

  /**
   * 开始任务
   */
  async start() {
    if (this.isRunning) {
      throw new Error('任务已在运行中')
    }

    this.log('info', `开始TikTok养号任务，设备数量: ${this.devices.length}`)

    try {
      // 初始化设备控制器
      await this.initializeDeviceControllers()

      // 启动所有设备任务
      await this.startAllDeviceTasks()

      this.isRunning = true
      this.startTime = Date.now()

      // 启动进度监控
      this.startProgressMonitoring()

      this.log('success', '所有设备任务已启动')
    }
    catch (error) {
      this.log('error', `启动任务失败: ${error.message}`)
      throw error
    }
  }

  /**
   * 暂停任务
   */
  async pause() {
    if (!this.isRunning || this.isPaused) {
      return
    }

    this.log('info', '正在暂停所有设备任务...')
    this.isPaused = true

    for (const controller of this.deviceControllers.values()) {
      await controller.pause()
    }

    this.log('warning', '所有设备任务已暂停')
  }

  /**
   * 继续任务
   */
  async resume() {
    if (!this.isRunning || !this.isPaused) {
      return
    }

    this.log('info', '正在继续所有设备任务...')
    this.isPaused = false

    for (const controller of this.deviceControllers.values()) {
      await controller.resume()
    }

    this.log('success', '所有设备任务已继续')
  }

  /**
   * 停止任务
   */
  async stop() {
    this.log('info', '正在停止所有设备任务...')

    this.isRunning = false
    this.isPaused = false

    // 停止进度监控
    if (this.taskTimer) {
      clearInterval(this.taskTimer)
      this.taskTimer = null
    }

    // 停止所有设备任务
    for (const controller of this.deviceControllers.values()) {
      await controller.stop()
    }

    this.deviceControllers.clear()
    this.log('info', '所有设备任务已停止')
  }

  /**
   * 初始化设备控制器
   */
  async initializeDeviceControllers() {
    this.log('info', '正在初始化设备控制器...')

    const initErrors = []

    for (const device of this.devices) {
      try {
        const controller = new DeviceController({
          device,
          config: this.config,
          onStatusUpdate: status => this.handleDeviceStatusUpdate(device.id, status),
          onLog: log => this.log(log.level, `[${device.id}] ${log.message}`),
        })

        await controller.initialize()
        this.deviceControllers.set(device.id, controller)

        this.log('success', `设备 ${device.id} 控制器初始化完成`)
      }
      catch (error) {
        this.log('error', `设备 ${device.id} 初始化失败: ${error.message}`)
        initErrors.push(`设备 ${device.id}: ${error.message}`)
      }
    }

    // 如果所有设备都初始化失败，抛出错误
    if (this.deviceControllers.size === 0) {
      throw new Error(`所有设备初始化失败:\n${initErrors.join('\n')}`)
    }

    // 如果部分设备初始化失败，记录警告
    if (initErrors.length > 0) {
      this.log('warning', `${initErrors.length} 个设备初始化失败，将继续使用其他设备`)
    }
  }

  /**
   * 启动所有设备任务
   */
  async startAllDeviceTasks() {
    const startPromises = []

    for (const [deviceId, controller] of this.deviceControllers) {
      startPromises.push(
        controller.start().catch((error) => {
          this.log('error', `设备 ${deviceId} 启动失败: ${error.message}`)
          this.handleDeviceStatusUpdate(deviceId, {
            status: 'error',
            error: error.message,
          })
        }),
      )
    }

    await Promise.allSettled(startPromises)
  }

  /**
   * 启动进度监控
   */
  startProgressMonitoring() {
    this.taskTimer = setInterval(() => {
      if (!this.isRunning || this.isPaused) {
        return
      }

      const elapsedTime = Math.floor((Date.now() - this.startTime) / 1000)
      const totalDuration = this.config.duration * 60

      // 发送总体进度更新
      this.onStatusUpdate({
        type: 'overall_progress',
        data: {
          elapsedTime,
          totalDuration,
          progress: Math.min(100, (elapsedTime / totalDuration) * 100),
        },
      })

      // 检查是否到达任务时长
      if (elapsedTime >= totalDuration) {
        this.completeTask()
      }
    }, 1000)
  }

  /**
   * 完成任务
   */
  async completeTask() {
    this.log('success', '任务时长已达到，正在完成任务...')

    await this.stop()

    this.onStatusUpdate({
      type: 'task_completed',
      data: {
        completedAt: new Date(),
        duration: this.config.duration * 60,
      },
    })
  }

  /**
   * 处理设备状态更新
   */
  handleDeviceStatusUpdate(deviceId, status) {
    this.onStatusUpdate({
      type: 'device_status',
      deviceId,
      data: status,
    })

    // 检查是否所有设备都完成或出错
    if (status.status === 'completed' || status.status === 'error') {
      this.checkAllDevicesCompleted()
    }
  }

  /**
   * 检查所有设备是否完成
   */
  checkAllDevicesCompleted() {
    const allCompleted = Array.from(this.deviceControllers.values()).every((controller) => {
      const status = controller.getStatus()
      return status === 'completed' || status === 'error'
    })

    if (allCompleted && this.isRunning) {
      this.completeTask()
    }
  }

  /**
   * 记录日志
   */
  log(level, message) {
    const timestamp = new Date().toLocaleTimeString()
    console.log(`[${timestamp}] [${level.toUpperCase()}] ${message}`)

    this.onLog({
      level,
      message,
      timestamp: new Date(),
    })
  }

  /**
   * 获取任务状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      isPaused: this.isPaused,
      startTime: this.startTime,
      deviceCount: this.devices.length,
      activeDevices: this.deviceControllers.size,
    }
  }

  /**
   * 获取设备统计信息
   */
  getDeviceStats() {
    const stats = {}

    for (const [deviceId, controller] of this.deviceControllers) {
      stats[deviceId] = controller.getStats()
    }

    return stats
  }
}
