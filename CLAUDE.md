# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

**Package Manager**: pnpm (required - specified in package.json)

```bash
# Development
pnpm dev              # Start development server with hot reload

# Building
pnpm build            # Build for current platform (auto-detect)
pnpm build:win        # Build for Windows
pnpm build:mac        # Build for macOS
pnpm build:linux      # Build for Linux

# Linting
pnpm lint             # Run ESLint
pnpm lint:fix         # Run ESLint with auto-fix

# Documentation
pnpm docs:dev         # Start VitePress dev server
pnpm docs:build       # Build documentation
pnpm docs:preview     # Preview built documentation

# Utilities
pnpm svgo             # Optimize SVG icons in src/icons/svgo/
pnpm electron-fix     # Fix Electron installation issues if needed
```

## Architecture Overview

Escrcpy is an Electron-based GUI for scrcpy that mirrors and controls Android devices. It uses Vue 3 + Electron with a dual-window architecture.

### Core Structure

- **Main Process**: `electron/main.js` - Electron main process, creates windows and manages system integration
- **Renderer Process**: `src/main.js` - Vue 3 application bootstrap 
- **Control Window**: `control/` - Separate floating control bar window for mirrored devices
- **IPC Bridge**: `electron/preload.js` - Secure communication bridge between main and renderer

### Key Components

- **Device Management**: `src/pages/device/` - Main interface for discovering, connecting, and managing Android devices
- **Settings**: `src/pages/preference/` - Application configuration with scrcpy parameter customization
- **State Management**: Pinia stores in `src/store/` (device, preference, task, theme)
- **External Tool Integration**: `electron/exposes/` exposes APIs for:
  - `adb/` - Android Debug Bridge operations via adbkit
  - `scrcpy/` - Screen mirroring and device control
  - `gnirehtet/` - Reverse tethering functionality

### Build & Configuration

- **Vite** with multi-entry build (main app + control window)
- **Auto-imports** for Vue APIs and components
- **UnoCSS** for styling with custom shades preset
- **ESLint** using @antfu/eslint-config
- **Vue Router** with file-based routing via unplugin-vue-router
- **Internationalization** with Vue I18n

### Development Notes

- Uses Vue 3 Composition API throughout
- Electron-store for persistent settings
- Custom IPC handlers in `electron/ipc/`
- Floating control window spawned per connected device
- External binaries managed in `electron/resources/extra/`