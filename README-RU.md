<div style="display:flex;">
  <img src="https://cdn.jsdelivr.net/gh/viarotel-org/escrcpy@main/electron/resources/build/logo.png" alt="viarotel-escrcpy" width="108px">
</div>

# Escrcpy

[![GitCode](https://gitcode.com/viarotel-org/escrcpy/star/badge.svg)](https://gitcode.com/viarotel-org/escrcpy)
[![Gitee](https://gitee.com/viarotel-org/escrcpy/badge/star.svg?theme=dark)](https://gitee.com/viarotel-org/escrcpy)
[![GitHub](https://img.shields.io/github/stars/viarotel-org/escrcpy?label=Github%20Stars)](https://github.com/viarotel-org/escrcpy)

📱 Отображайте и управляйте своим Android-устройством с помощью scrcpy в графическом режиме, работающим на Electron. [Документация на китайском](https://github.com/viarotel-org/escrcpy/blob/main/README-CN.md)

<div style="display:flex;">
  <img src="./screenshots/ru-RU/overview.jpg" alt="viarotel-escrcpy" width="100%">
</div>

## Возможности

- 🏃 Синхронизация: Более быстрая синхронизация с Scrcpy благодаря веб-технологиям
- 🤖 Автоматизация: Автоподключение устройств, автоматическое зеркалирование, пользовательские скрипты, запланированные задачи
- 💡 Настройка: Управление несколькими устройствами, независимые конфигурации, пользовательские заметки, импорт/экспорт настроек
- 📡 Беспроводное подключение: Быстрое подключение через сканирование QR-кода
- 🔗 Обратная привязка: Обратная привязка Gnirehtet
- 🎨 Темы: Светлый режим, темный режим, следование системной теме
- 😎 Легковесность: Нативная поддержка, отображает только экран устройства
- ⚡️ Производительность: 30~120 FPS в зависимости от устройства
- 🌟 Качество: 1920×1080 или выше
- 🕒 Низкая задержка: 35~70 мс
- 🚀 Быстрый запуск: Первое изображение отображается примерно через 1 секунду
- 🙅‍♂️ Ненавязчивость: Не оставляет файлов установки на Android-устройствах
- 🤩 Преимущества для пользователей: Нет аккаунтов, нет рекламы, не требует интернет-соединения
- 🗽 Свобода: Бесплатное и открытое программное обеспечение

## Установка

### Ручная установка через выпущенные пакеты

Проверьте [страницу релизов](https://github.com/viarotel-org/escrcpy/releases)

### Установка на macOS через Homebrew

См. [homebrew-escrcpy](https://github.com/viarotel-org/homebrew-escrcpy)

## Документация

- [Начало работы](https://escrcpy.viarotel.eu.org/guide/started)
- [Горячие клавиши](https://escrcpy.viarotel.eu.org/reference/scrcpy/shortcuts)
- [Операции с устройством](https://escrcpy.viarotel.eu.org/guide/operation)
- [Настройки](https://escrcpy.viarotel.eu.org/guide/preferences)
- [Обратная привязка](https://escrcpy.viarotel.eu.org/reference/gnirehtet/)

## Для разработчиков

Если вы разработчик и хотите запустить или помочь улучшить этот проект, обратитесь к [документации по разработке](https://github.com/viarotel-org/escrcpy/blob/main/develop.md)

## Получение помощи

Как проект с открытым исходным кодом, созданный на энтузиазме, поддержка ограничена, а обновления нерегулярны.

- [FAQ](https://escrcpy.viarotel.eu.org/help/escrcpy)
- [Сообщить о проблеме](https://github.com/viarotel-org/escrcpy/issues)
- [Контактный email](<EMAIL>)

## Что дальше?

[Вехи](https://escrcpy.viarotel.eu.org/guide/milestones)

## Благодарности

Этот проект существует благодаря следующим проектам с открытым исходным кодом:

- [scrcpy](https://github.com/Genymobile/scrcpy)
- [adbkit](https://github.com/DeviceFarmer/adbkit)
- [electron](https://www.electronjs.org/)
- [vue](https://vuejs.org/)
- [gnirehtet](https://github.com/Genymobile/gnirehtet/)

## Поддержка

Если этот проект помог вам, рассмотрите возможность купить мне кофе, чтобы мотивировать меня на дальнейшие улучшения 😛

<div style="display:flex;">
  <img src="https://cdn.jsdelivr.net/gh/viarotel-org/escrcpy@main/src/assets/sponsor/viarotel-wepay.png" alt="viarotel-wepay" width="30%">
  <img src="https://cdn.jsdelivr.net/gh/viarotel-org/escrcpy@main/src/assets/sponsor/viarotel-alipay.png" alt="viarotel-alipay" width="30%">
  <a href="https://www.paypal.com/paypalme/viarotel" target="_blank" rel="noopener noreferrer">
    <img src="https://cdn.jsdelivr.net/gh/viarotel-org/escrcpy@main/src/assets/sponsor/viarotel-paypal.png" alt="viarotel-paypal" width="30%">
  </a>
</div>

## Участники

Спасибо всем, кто внес свой вклад!

[Участники](https://github.com/viarotel/escrcpy/graphs/contributors)

## История звезд

[![Star History Chart](https://api.star-history.com/svg?repos=viarotel-org/escrcpy&type=Date)](https://star-history.com/#viarotel-org/escrcpy&Date)