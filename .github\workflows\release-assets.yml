name: release-assets

on:
  workflow_dispatch:
  push:
    tags:
      - v*.*.*

jobs:
  release:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            arch: [arm64, amd64]
          - os: macos-latest
            arch: [arm64, amd64]
          - os: windows-latest
            arch: [arm64, amd64]
    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Enable corepack and pnpm
        run: |
          corepack enable
          corepack prepare pnpm@9.13.2 --activate

      - name: Install Dependencies
        run: pnpm install

      - name: build-linux
        if: matrix.os == 'ubuntu-latest'
        run: pnpm build:linux
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN }}

      - name: build-mac
        if: matrix.os == 'macos-latest'
        run: pnpm build:mac
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN }}

      - name: build-win
        if: matrix.os == 'windows-latest'
        run: pnpm build:win
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN }}

      - name: release
        uses: softprops/action-gh-release@v2
        with:
          draft: false
          prerelease: false
          fail_on_unmatched_files: false
          files: |
            dist-release/*.exe
            dist-release/*.zip
            dist-release/*.dmg
            dist-release/*.AppImage
            dist-release/*.snap
            dist-release/*.deb
            dist-release/*.rpm
            dist-release/*.tar.gz
            dist-release/*.blockmap
            dist-release/latest.yml
            dist-release/latest-mac.yml
            dist-release/latest-linux.yml
            dist-release/latest-linux-arm64.yml
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}