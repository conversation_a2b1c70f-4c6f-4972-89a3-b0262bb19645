/**
 * 设备控制器
 * 负责单个设备的TikTok自动化操作
 */

import { randomBetween, sleep } from '$/utils/index.js'

export default class DeviceController {
  constructor(options) {
    this.device = options.device
    this.config = options.config
    this.onStatusUpdate = options.onStatusUpdate || (() => {})
    this.onLog = options.onLog || (() => {})

    this.isRunning = false
    this.isPaused = false
    this.shouldStop = false

    // 设备信息
    this.deviceInfo = {
      id: this.device.id,
      resolution: null,
      density: null,
      tiktokPackage: 'com.zhiliaoapp.musically', // TikTok包名
    }

    // 操作统计
    this.stats = {
      processedVideos: 0,
      likeCount: 0,
      followCount: 0,
      favoriteCount: 0,
      swipeCount: 0,
      errors: 0,
    }

    // 操作坐标（根据分辨率计算）
    this.coordinates = {}

    this.log('info', '设备控制器已创建')
  }

  /**
   * 初始化设备
   */
  async initialize() {
    this.log('info', '正在初始化设备...')

    try {
      // 获取设备信息
      await this.getDeviceInfo()

      // 计算操作坐标
      this.calculateCoordinates()

      // 检查TikTok应用
      await this.checkTikTokApp()

      this.updateStatus('准备就绪')
      this.log('success', '设备初始化完成')
    }
    catch (error) {
      this.log('error', `设备初始化失败: ${error.message}`)
      throw error
    }
  }

  /**
   * 开始任务
   */
  async start() {
    if (this.isRunning) {
      return
    }

    this.log('info', '开始TikTok养号任务')
    this.isRunning = true
    this.shouldStop = false

    try {
      // 启动TikTok应用
      await this.launchTikTok()

      // 等待应用启动
      await this.interruptibleSleep(3000)

      // 开始主循环
      await this.mainLoop()
    }
    catch (error) {
      this.log('error', `任务执行失败: ${error.message}`)
      this.updateStatus('错误', error.message)
      this.stats.errors++
    }
    finally {
      this.isRunning = false
    }
  }

  /**
   * 暂停任务
   */
  async pause() {
    this.isPaused = true
    this.updateStatus('已暂停')
    this.log('warning', '任务已暂停')
  }

  /**
   * 继续任务
   */
  async resume() {
    this.isPaused = false
    this.updateStatus('运行中')
    this.log('success', '任务已继续')
  }

  /**
   * 停止任务
   */
  async stop() {
    this.shouldStop = true
    this.isRunning = false
    this.isPaused = false
    this.updateStatus('已停止')
    this.log('info', '任务已停止')
  }

  /**
   * 可中断的sleep方法
   * @param {number} ms 毫秒数
   * @returns {Promise<void>}
   */
  async interruptibleSleep(ms) {
    const checkInterval = 100 // 每100ms检查一次停止标志
    const iterations = Math.ceil(ms / checkInterval)

    for (let i = 0; i < iterations; i++) {
      if (this.shouldStop) {
        return // 立即返回，不再等待
      }
      await sleep(Math.min(checkInterval, ms - i * checkInterval))
    }
  }

  /**
   * 获取设备信息
   */
  async getDeviceInfo() {
    try {
      // 获取屏幕分辨率
      const sizeOutput = await window.adb.deviceShell(this.device.id, 'wm size')
      this.log('info', `sizeOutput: ${sizeOutput}`)
      const sizeMatch = sizeOutput.match(/(\d+)x(\d+)/)
      if (sizeMatch) {
        this.deviceInfo.resolution = {
          width: Number.parseInt(sizeMatch[1]),
          height: Number.parseInt(sizeMatch[2]),
        }
      }

      // 获取屏幕密度
      const densityOutput = await window.adb.deviceShell(this.device.id, 'wm density')
      this.log('info', `densityOutput: ${densityOutput}`)
      const densityMatch = densityOutput.match(/(\d+)/)
      if (densityMatch) {
        this.deviceInfo.density = Number.parseInt(densityMatch[1])
      }

      this.log('info', `设备分辨率: ${this.deviceInfo.resolution.width}x${this.deviceInfo.resolution.height}`)
      this.log('info', `设备密度: ${this.deviceInfo.density}`)
    }
    catch (error) {
      throw new Error(`获取设备信息失败: ${error.message}`)
    }
  }

  /**
   * 计算操作坐标
   */
  calculateCoordinates() {
    const { width, height } = this.deviceInfo.resolution

    // 根据分辨率计算各种操作的坐标
    this.coordinates = {
      // 点赞按钮（右侧中下部）
      like: {
        x: Math.floor(width * 0.55),
        y: Math.floor(height * 0.75),
      },

      // 关注按钮（右侧中部）
      follow: {
        x: Math.floor(width * 0.55),
        y: Math.floor(height * 0.36),
      },

      // 收藏按钮（右侧下部）
      favorite: {
        x: Math.floor(width * 0.55),
        y: Math.floor(height * 0.52),
      },

      // 滑动区域（屏幕中央）
      swipeStart: {
        x: Math.floor(width * 0.5),
        y: Math.floor(height * 0.6),
      },
      swipeEnd: {
        x: Math.floor(width * 0.5),
        y: Math.floor(height * 0.2),
      },

      // 评论按钮（右侧中下部）
      comment: {
        x: Math.floor(width * 0.55),
        y: Math.floor(height * 0.44),
      },
    }

    this.log('info', '操作坐标已计算完成')
  }

  /**
   * 检查TikTok应用
   */
  async checkTikTokApp() {
    try {
      const packages = await window.adb.deviceShell(this.device.id, 'pm list packages')
      if (!packages.includes(this.deviceInfo.tiktokPackage)) {
        throw new Error('设备上未安装TikTok应用')
      }
      this.log('success', 'TikTok应用检查通过')
    }
    catch (error) {
      throw new Error(`TikTok应用检查失败: ${error.message}`)
    }
  }

  /**
   * 启动TikTok应用
   */
  async launchTikTok() {
    try {
      await window.adb.deviceShell(this.device.id, `monkey -p com.zhiliaoapp.musically -c android.intent.category.LAUNCHER 1`)
      this.updateStatus('启动TikTok')
      this.log('info', 'TikTok应用已启动')
    }
    catch (error) {
      throw new Error(`启动TikTok失败: ${error.message}`)
    }
  }

  /**
   * 主循环
   */
  async mainLoop() {
    this.updateStatus('运行中')

    while (this.isRunning && !this.shouldStop) {
      try {
        // 检查暂停状态
        if (this.isPaused) {
          await this.interruptibleSleep(1000)
          continue
        }

        // 检查停止标志
        if (this.shouldStop) break

        // 观看视频
        await this.watchVideo()

        // 检查停止标志
        if (this.shouldStop) break

        // 随机执行操作
        await this.performRandomActions()

        // 检查停止标志
        if (this.shouldStop) break

        // 滑动到下一个视频
        await this.swipeToNext()

        this.stats.processedVideos++
        this.updateStats()

        // 随机延迟
        if (this.config.randomDelay) {
          const delay = randomBetween(500, 2000)
          await this.interruptibleSleep(delay)
        }
      }
      catch (error) {
        this.log('error', `主循环执行错误: ${error.message}`)
        this.stats.errors++
        await this.interruptibleSleep(2000) // 错误后等待2秒
      }
    }

    if (!this.shouldStop) {
      this.updateStatus('已完成')
      this.log('success', '任务执行完成')
    }
  }

  /**
   * 观看视频
   */
  async watchVideo() {
    const watchTime = this.config.humanLike
      ? randomBetween(this.config.videoWatchTime * 0.8, this.config.videoWatchTime * 1.2)
      : this.config.videoWatchTime

    this.updateStatus(`观看视频 (${Math.floor(watchTime)}秒)`)
    await this.interruptibleSleep(watchTime * 1000)
  }

  /**
   * 执行随机操作
   */
  async performRandomActions() {
    // 点赞
    if (Math.random() * 100 < this.config.likeFrequency) {
      await this.performLike()
    }

    // 关注
    if (Math.random() * 100 < this.config.followFrequency) {
      await this.performFollow()
    }

    // 收藏
    if (Math.random() * 100 < this.config.favoriteFrequency) {
      await this.performFavorite()
    }
  }

  /**
   * 执行点赞
   */
  async performLike() {
    try {
      this.updateStatus('点赞')
      await this.tap(this.coordinates.like.x, this.coordinates.like.y)
      this.stats.likeCount++
      this.log('info', '执行点赞操作')

      if (this.config.humanLike) {
        await this.interruptibleSleep(randomBetween(300, 800))
      }
    }
    catch (error) {
      this.log('error', `点赞操作失败: ${error.message}`)
    }
  }

  /**
   * 执行关注
   */
  async performFollow() {
    try {
      this.updateStatus('关注')
      await this.tap(this.coordinates.follow.x, this.coordinates.follow.y)
      this.stats.followCount++
      this.log('info', '执行关注操作')

      if (this.config.humanLike) {
        await this.interruptibleSleep(randomBetween(500, 1200))
      }
    }
    catch (error) {
      this.log('error', `关注操作失败: ${error.message}`)
    }
  }

  /**
   * 执行收藏
   */
  async performFavorite() {
    try {
      this.updateStatus('收藏')
      await this.tap(this.coordinates.favorite.x, this.coordinates.favorite.y)
      this.stats.favoriteCount++
      this.log('info', '执行收藏操作')

      if (this.config.humanLike) {
        await this.interruptibleSleep(randomBetween(300, 700))
      }
    }
    catch (error) {
      this.log('error', `收藏操作失败: ${error.message}`)
    }
  }

  /**
   * 滑动到下一个视频
   */
  async swipeToNext() {
    try {
      this.updateStatus('滑动')

      const { swipeStart, swipeEnd } = this.coordinates
      const duration = this.config.humanLike ? randomBetween(300, 600) : 400

      await this.swipe(swipeStart.x, swipeStart.y, swipeEnd.x, swipeEnd.y, duration)
      this.stats.swipeCount++

      // 等待滑动完成和视频加载
      const interval = this.config.humanLike
        ? randomBetween(this.config.swipeInterval * 0.8, this.config.swipeInterval * 1.2)
        : this.config.swipeInterval

      await this.interruptibleSleep(interval * 1000)
    }
    catch (error) {
      this.log('error', `滑动操作失败: ${error.message}`)
    }
  }

  /**
   * 点击操作
   */
  async tap(x, y) {
    this.log('info', `点击 (${x}, ${y})`)
    const tap = await window.adb.deviceShell(this.device.id, `input tap ${x} ${y}`)
    this.log('info', `tap (${tap})`)
  }

  /**
   * 滑动操作
   */
  async swipe(x1, y1, x2, y2, duration = 300) {
    this.log('info', `滑动 (${x1}, ${y1})->(${x2}, ${y2}) (${duration}ms)`)
    const swipe = await window.adb.deviceShell(this.device.id, `input swipe ${x1} ${y1} ${x2} ${y2} 300`)
    this.log('info', `swipe (${swipe})`)
  }

  /**
   * 更新状态
   */
  updateStatus(currentAction, error = null) {
    const status = {
      status: this.isRunning ? 'running' : (error ? 'error' : 'idle'),
      currentAction,
      progress: this.calculateProgress(),
      error,
    }

    this.onStatusUpdate(status)
  }

  /**
   * 更新统计信息
   */
  updateStats() {
    this.onStatusUpdate({
      ...this.stats,
      progress: this.calculateProgress(),
    })
  }

  /**
   * 计算进度
   */
  calculateProgress() {
    // 基于处理的视频数量计算进度
    const estimatedVideosPerMinute = 60 / (this.config.videoWatchTime + this.config.swipeInterval)
    const totalEstimatedVideos = estimatedVideosPerMinute * this.config.duration

    return Math.min(100, (this.stats.processedVideos / totalEstimatedVideos) * 100)
  }

  /**
   * 记录日志
   */
  log(level, message) {
    this.onLog({ level, message })
  }

  /**
   * 获取状态
   */
  getStatus() {
    return this.isRunning ? 'running' : (this.isPaused ? 'paused' : 'idle')
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return { ...this.stats }
  }
}
