import { resolve } from 'node:path'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import UnoCSS from 'unocss/vite'
import vueRouter from 'unplugin-vue-router/vite'
import vueI18n from '@intlify/unplugin-vue-i18n/vite'
import electron from 'vite-plugin-electron/simple'
import renderer from 'vite-plugin-electron-renderer'
import svg from 'vite-svg-loader'
import autoImports from './src/plugins/auto.js'
import postcssConfig from './postcss.config.js'

const alias = {
  $: resolve('src'),
  $root: resolve(),
  $docs: resolve('docs'),
  $renderer: resolve('src'),
  $electron: resolve('electron'),
  $control: resolve('control'),
}

export default defineConfig(({ command }) => ({
  build: {
    rollupOptions: {
      input: {
        main: resolve('index.html'),
        control: resolve('control/index.html'),
      },
    },
  },
  resolve: {
    alias,
  },
  plugins: [
    UnoCSS(),
    svg(),
    vueRouter({
      exclude: ['src/pages/**/components'],
    }),
    vue(),
    vueJsx(),
    vueI18n({
      include: [resolve('src/locales/languages/**')],
    }),
    electron({
      main: {
        entry: 'electron/main.js',
      },
      preload: {
        input: 'electron/preload.js',
      },
    }),
    renderer(),
    ...autoImports(),
  ],
  css: {
    postcss: postcssConfig,
  },
}))
