---  
title: 指引  
---

# Escrcpy：基于Electron的图形化安卓设备控制工具  

## 什么是Escrcpy？  

Escrcpy是一款基于Electron开发的图形化安卓设备控制工具。它基于知名的开源项目Scrcpy构建，为用户提供了更加友好且功能丰富的图形界面。通过Escrcpy，用户可以轻松地在电脑上显示并控制安卓设备，享受高清、低延迟的投屏体验。  

## 核心功能  

### 高效稳定的投屏体验  
- ⚡️ 高性能：支持30-120 fps（取决于设备性能）  
- 🌟 高质量：支持1920×1080或更高分辨率显示  
- 🕒 低延迟：延迟仅35~70毫秒  
- 🚀 快速启动：约1秒即可显示首帧画面  

### 丰富的功能特性  
- 📡 无线连接：支持通过扫码快速连接设备  
- 🤖 自动化：自动连接设备、自动执行投屏、自定义脚本及定时任务  
- 💡 高度可定制：多设备管理、独立配置、自定义备注、配置导入导出  
- 🔗 反向网络共享：内置Gnirehtet反向网络共享功能  
- 🎨 主题切换：支持浅色/深色模式，随系统自动切换  

### 优化的用户体验  
- 😎 轻量化：原生支持，仅显示设备屏幕  
- 🙅‍♂️ 无残留：不会在安卓设备上留下任何痕迹  
- 🤩 纯净体验：无需账户、无广告、无需联网  
- 🗽 完全免费开源  

## 使用场景  

Escrcpy非常适合以下场景：  
1. 开发者在电脑上调试安卓应用  
2. 游戏玩家在大屏幕上畅玩手游  
3. 需要录制手机屏幕内容的用户  
4. 工作中需在电脑上操作手机的情况  
5. 教学时演示手机操作  

## 跨平台支持  

Escrcpy支持主流操作系统：  
- Windows  
- macOS  
- Linux  

## 技术优势  

作为Scrcpy的图形界面版本，Escrcpy保留了Scrcpy的全部优点，同时新增了：  
- 更直观的设备管理界面  
- 更便捷的连接方式  
- 更丰富的自定义选项  
- 更完善的批量操作功能  

## 未来发展  

项目团队将持续完善Escrcpy，未来计划包括：  
- 优化投屏窗口位置与大小的设置方式  
- 提升批量设备连接体验  
- 开发图形化脚本编辑工具  

## 如何使用  

Escrcpy是一个完全免费的开源项目，具体使用方法请参考：  

[快速开始 👉](/zhHans/guide/started)  

## 结语  

Escrcpy为Scrcpy带来了现代化的图形界面和增强功能，让安卓设备控制变得更简单高效。无论你是开发者还是普通用户，Escrcpy都能为你提供出色的设备控制体验。其开源免费的特性，也让更多人能够毫无顾虑地使用这一优秀工具。  

如果你正在寻找一款强大且易用的安卓设备控制工具，Escrcpy绝对值得一试！  